package com.snct.netty.pdu;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;
import com.snct.dctcore.commoncore.analysis.BaseAnalysis;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.DeviceCategoryEnum;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.device.domain.SocketEntity;
import com.snct.netty.DeviceHandler;
import com.snct.netty.DeviceMessageListener;
import com.snct.system.domain.Device;
import com.snct.system.domain.msg.BuMsgPdu;
import com.snct.system.domain.msg.BuMsgPduOut;
import com.snct.utils.HexUtil2;
import com.snct.web.controller.business.DeviceController;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;
import java.net.InetSocketAddress;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * PDU设备消息处理器
 * 处理PDU设备的消息解析和发送
 */
@Service
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PduDeviceHandler extends DeviceHandler {

    @Autowired
    private RedisTemplate redisTemplate;

    private static final Logger logger = LoggerFactory.getLogger(PduDeviceHandler.class);

    SocketEntity socketEntity = null;

    private static final String PDU_DATA = CacheConstants.DEVICE_DATA_KEY + "pdu";

    // 消息监听器列表
    private final List<DeviceMessageListener> listeners = new ArrayList<>();

    // 当前通道
    private Channel channel;

    // 设备地址
    private String deviceAddress;

    // 设备对象
    private Device device;

    // 是否收集完所有数据
    private boolean dataCollectionComplete = false;

    // 收到的通道数据数量计数
    private int receivedChannelCount = 0;

    // 通道数据是否全部已收集
    private boolean allChannelsCollected = false;
    
    // 当前批次的通道输出数据缓存，用于合并发送
    private final List<BuMsgPduOut> channelOutputBuffer = new ArrayList<>();

    // 命令序列
    private final String[] queryCommands = {
            "5506010100010000", // 查询通道1
            "5506010100020000", // 查询通道2
            "5506010100030000", // 查询通道3
            "5506010100040000", // 查询通道4
            "5506010100050000", // 查询通道5
            "5506010100060000", // 查询通道6
            "5506010100070000", // 查询通道7
            "5506010100080000", // 查询通道8
            "5506010500000000", // 查询系统功率
            "5506010200000000", // 查询输入电流
            "5506010300000000", // 查询输入电压
            "5506010400000000"  // 查询总电能
    };

    // 需要采集的通道数量
    private static final int TOTAL_CHANNELS = 8;

    // PDU数据
    private BuMsgPdu pduData = new BuMsgPdu();

    // PDU输出通道数据
    private Map<Integer, BuMsgPduOut> pduOutMap = new HashMap<>();

    // 是否已经发送了数据
    private boolean dataSent = false;

    // ========== 新增：原始数据收集器 ==========
    // 原始数据收集器
    private final List<String> rawDataCollection = new ArrayList<>();
    // 收集开始时间
    private long collectionStartTime = 0;
    // 收集超时时间（毫秒）
    private static final long COLLECTION_TIMEOUT = 3000;
    @Autowired
    private RedisCache redisCache;

    /**
     * 设置设备对象
     *
     */
    public void setDevice(Device device) {
        this.device = device;
        // 初始化PDU数据对象
        this.pduData.setDeviceId(device != null ? device.getId() : null);
    }

    /**
     * 获取设备对象
     *
     */
    public Device getDevice() {
        return this.device;
    }

    /**
     * 获取船舶SN号
     *
     */
    public String getSn() {
        return device != null ? device.getSn() : null;
    }

    /**
     * 添加消息监听器
     */
    public void addMessageListener(DeviceMessageListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 获取PDU数据
     */
    public BuMsgPdu getPduData() {
        return this.pduData;
    }

    /**
     * 获取PDU输出通道数据
     */
    public Map<Integer, BuMsgPduOut> getPduOutData() {
        return this.pduOutMap;
    }

    /**
     * 移除消息监听器
     */
    public void removeMessageListener(DeviceMessageListener listener) {
        listeners.remove(listener);
    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        this.channel = ctx.channel();
        this.deviceAddress = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
        logger.info("PDU设备通道已激活: {}", deviceAddress);
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("PDU设备通道已关闭: {}", deviceAddress);
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (!ctx.channel().isOpen()) {
            return;
        }

        // 消息已经被解码为byte[]
        if (!(msg instanceof byte[])) {
            logger.warn("接收到的消息不是byte[]类型: {}", msg.getClass().getName());
            return;
        }

        // 获取消息内容（字节数组）
        byte[] response = (byte[]) msg;

        // 打印原始消息
        String hexString = HexUtil2.byteArrayToHex(response);

        // 将消息保存到内存中，用于预览
        try {
            // 获取设备编码
            String deviceCode = device != null ? device.getCode() : null;
            // 只有在设备有编码时才可能存放预览数据
            if (deviceCode != null && !deviceCode.isEmpty()) {
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                    // 获取当前时间并格式化
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    // 构建key: 设备编码###时间
                    String key = deviceCode + "###" + formattedTime;
                    // 保存原始数据到viewValue
                    DeviceController.viewValue.put(key, hexString);
                }
            }
        } catch (Exception e) {
            logger.error("保存预览数据异常", e);
        }

        logger.debug("收到PDU消息: {} 来自: {}", hexString, deviceAddress);

        // 解析消息 (去掉空格)
        String hexData = hexString.replaceAll(" ", "").trim();
        if (hexData.length() < 4) {
            logger.warn("消息格式错误，长度不足: {}", hexData);
            return;
        }

        // 使用新的数据收集方式
        collectRawData(hexData);
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        logger.error("PDU设备通信异常: {}", cause.getMessage(), cause);
        ctx.close();
    }

    @Override
    public String getDeviceType() {
        return "PDU";
    }

    @Override
    public boolean sendQueryCommand(List<String> commands) {
        if (channel == null || !channel.isActive()) {
            logger.error("PDU设备未连接，无法发送查询命令");
            return false;
        }

        try {
            for (String command : commands){

                // 发送命令
                byte[] bytes = HexUtil2.hexToByteArray(command);
                channel.writeAndFlush(bytes);

                logger.debug("发送PDU查询命令: {}", command);
            }
            return true;
        } catch (Exception e) {
            logger.error("发送PDU查询命令时发生异常", e);
            return false;
        }
    }

    // ====================发送查询命令=======================

    /**
     * 一次性发送所有查询命令，并为结果分配同一个批次号
     *
     * @param delayMs 命令间延时(毫秒)
     * @return 是否成功发送所有查询命令
     */
    public boolean sendAllQueryCommands(int delayMs) {
        if (channel == null || !channel.isActive()) {
            logger.error("PDU设备未连接，无法发送查询命令");
            return false;
        }

        try {
            // 重置数据收集状态，准备新一轮采集
            resetDataCollectionStatus();

            // 重置原始数据收集器
            rawDataCollection.clear();

            // 清除通道数据 - 不再单独设置批次号
            pduOutMap.clear();

            for (String command : queryCommands) {
                // 发送命令
                byte[] bytes = HexUtil2.hexToByteArray(command);
                channel.writeAndFlush(bytes);
                logger.debug("发送PDU查询命令: {}", command);

                // 命令之间添加短暂延时
                if (delayMs > 0 && command != queryCommands[queryCommands.length - 1]) {
                    Thread.sleep(delayMs);
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("批量发送PDU查询命令时发生异常", e);
            return false;
        }
    }

    // ========== 新增：原始数据收集方法 ==========

    /**
     * 收集原始数据
     */
    private void collectRawData(String hexData) {
        // 如果是新的收集周期，重置收集器
        if (rawDataCollection.isEmpty()) {
            collectionStartTime = System.currentTimeMillis();
            logger.debug("开始新的PDU数据收集周期");
        }

        // 添加到收集器
        rawDataCollection.add(hexData);
        logger.debug("收集PDU原始数据: {}, 当前收集数量: {}", hexData, rawDataCollection.size());

        // 检查是否收集完成或超时
        if (isCollectionComplete() || isCollectionTimeout()) {
            // 调用统一解析方法
            processBatchData();
        }
    }

    /**
     * 处理批量数据
     */
    private void processBatchData() {
        try {
            logger.info("PDU数据收集完成，开始批量解析，数据包数量: {}", rawDataCollection.size());

            // 创建KafkaMessage对象来调用新的解析方法
            KafkaMessage kafkaMessage = new KafkaMessage();

            // 将收集的数据用逗号连接
            String combinedData = String.join(",", rawDataCollection);
            kafkaMessage.setMsg(combinedData);
            kafkaMessage.setInitialTime(System.currentTimeMillis());
            kafkaMessage.setType(Math.toIntExact(device.getType()));
            kafkaMessage.setSn(device.getSn());
            kafkaMessage.setCode(device.getCode());
            kafkaMessage.setCost(10);

            Object analysisData = BaseAnalysis.analysisData(kafkaMessage, null, DeviceCategoryEnum.TYPE_1.getValue());
            PduHbaseVo pduHbaseVo = (PduHbaseVo) analysisData;

            if (pduHbaseVo != null) {
                logger.info("PDU数据解析成功，设备: {}", device.getCode());
                redisCache.setCacheObject(PDU_DATA, analysisData);
                kafkaMessage.setMsg(JSONObject.toJSONString(pduHbaseVo));
                sendSocketToClient(kafkaMessage);

            } else {
                logger.warn("PDU数据解析失败，设备: {}", device.getCode());
            }

        } catch (Exception e) {
            logger.error("PDU批量数据处理失败", e);
        } finally {
            // 清空收集器，准备下一批次
            rawDataCollection.clear();
        }
    }

    /**
     * 检查收集是否完成
     */
    private boolean isCollectionComplete() {
        // 简单判断：收到预期数量的数据包（8个通道 + 4个系统数据）
        return rawDataCollection.size() >= 12;
    }

    /**
     * 检查收集是否超时
     */
    private boolean isCollectionTimeout() {
        return System.currentTimeMillis() - collectionStartTime > COLLECTION_TIMEOUT;
    }

    /**
     * 发送数据到客户端
     */
    private void sendSocketToClient(com.snct.dctcore.commoncore.domain.KafkaMessage kafkaMessage) {
        Long recordNum = System.currentTimeMillis() / 1000;
        TransferPackage transferPackage = new TransferPackage(recordNum, kafkaMessage.getInitialTime(),
                PackageTypeEnum.DEVICE_DATA.getValue(), 0, kafkaMessage.getMsg(),
                kafkaMessage.getType(), kafkaMessage.getCode());
        transferPackage.setSn(kafkaMessage.getSn());

        SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
        opsForSet.add(RedisParameter.SHIP_REPAIR_DATA_SET, transferPackage.getCommandNum());
        saveData2Redis(transferPackage, kafkaMessage.getCost());
    }

    /**
     * 保存数据到Redis
     */
    public void saveData2Redis(TransferPackage transferPackage, Integer cost) {
        ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();
        String key = RedisParameter.SHIP_DO_SEND_LIST_COST + cost;
        opsForList.rightPush(key, transferPackage);
    }

    /**
     * 重置数据收集状态
     * 在开始新一轮查询时调用
     */
    private void resetDataCollectionStatus() {
        dataCollectionComplete = false;
        receivedChannelCount = 0;
        allChannelsCollected = false;
        channelOutputBuffer.clear();
        dataSent = false;

        // 重置原始数据收集器
        rawDataCollection.clear();
        collectionStartTime = 0;
    }

} 