-- 1. 姿态仪数据表
CREATE TABLE `bu_data_attitude`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`    bigint       DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time` datetime     DEFAULT NULL COMMENT '录入时间(北京时间)',
    `utc_time`        varchar(50)  DEFAULT '' COMMENT 'UTC时间',
    `lat`             varchar(50)  DEFAULT '' COMMENT '纬度',
    `lon`             varchar(50)  DEFAULT '' COMMENT '经度',
    `rolling`         varchar(50)  DEFAULT '' COMMENT '横摇',
    `pitch`           varchar(50)  DEFAULT '' COMMENT '纵摇',
    `height`          varchar(50)  DEFAULT '' COMMENT '高度',
    `heading`         varchar(50)  DEFAULT '' COMMENT '航向',
    `speed`           varchar(50)  DEFAULT '' COMMENT '速度',
    `distance`        varchar(50)  DEFAULT '' COMMENT '距离',
    `station_name`    varchar(100) DEFAULT '' COMMENT '站名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='姿态仪数据表';

-- 2. GPS数据表
CREATE TABLE `bu_data_gps`
(
    `id`                   bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`         bigint      DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time`      datetime    DEFAULT NULL COMMENT '录入时间(北京时间)',
    `utc_time`             varchar(50) DEFAULT '' COMMENT 'UTC时间',
    `latitude_hemisphere`  varchar(20) DEFAULT '' COMMENT '纬度半球',
    `latitude`             varchar(50) DEFAULT '' COMMENT '纬度',
    `longitude_hemisphere` varchar(20) DEFAULT '' COMMENT '经度半球',
    `longitude`            varchar(50) DEFAULT '' COMMENT '经度',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GPS数据表';

-- 3. AWS气象数据表
CREATE TABLE `bu_data_aws`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`        bigint      DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time`     datetime    DEFAULT NULL COMMENT '录入时间(北京时间)',
    `relative_wind`       varchar(50) DEFAULT '' COMMENT '相对风向',
    `relative_wind_speed` varchar(50) DEFAULT '' COMMENT '相对风速',
    `air_temperature`     varchar(50) DEFAULT '' COMMENT '气温值',
    `air_unit`           varchar(50) DEFAULT '' COMMENT '气温单位',
    `humidity`            varchar(50) DEFAULT '' COMMENT '相对湿度数值',
    `humidity_unit`       varchar(50) DEFAULT '' COMMENT '相对湿度单位',
    `pressure`            varchar(50) DEFAULT '' COMMENT '气压数值',
    `pressure_unit`       varchar(50) DEFAULT '' COMMENT '气压单位',
    `utc_time`            varchar(50) DEFAULT '' COMMENT 'UTC时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AWS气象数据表';

-- 4. 卫星猫数据表
CREATE TABLE `bu_data_modem`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`    bigint      DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time` datetime    DEFAULT NULL COMMENT '录入时间(北京时间)',
    `signal`          varchar(50) DEFAULT '' COMMENT '信号强度',
    `speed`           varchar(50) DEFAULT '' COMMENT '速率',
    `send_power`      varchar(50) DEFAULT '' COMMENT '发送功率',
    `flag`            varchar(20) DEFAULT '' COMMENT '状态标志',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卫星猫数据表';

-- 5. PDU电源分配单元数据表
CREATE TABLE `bu_data_pdu`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`    bigint      DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time` datetime    DEFAULT NULL COMMENT '录入时间(北京时间)',
    `manage`          varchar(50) DEFAULT '' COMMENT '总电能',
    `electric`        varchar(50) DEFAULT '' COMMENT '电流',
    `voltage`         varchar(50) DEFAULT '' COMMENT '电压',
    `yes_power`       varchar(50) DEFAULT '' COMMENT '有功功率',
    `no_power`        varchar(50) DEFAULT '' COMMENT '无功功率',
    `see_power`       varchar(50) DEFAULT '' COMMENT '视在功率',
    `power_param`     varchar(50) DEFAULT '' COMMENT '功率因数',
    `out1_electric`   varchar(50) DEFAULT '' COMMENT '通道1电流',
    `out1_power`      varchar(50) DEFAULT '' COMMENT '通道1功率',
    `out1_status`     varchar(20) DEFAULT '' COMMENT '通道1状态',
    `out2_electric`   varchar(50) DEFAULT '' COMMENT '通道2电流',
    `out2_power`      varchar(50) DEFAULT '' COMMENT '通道2功率',
    `out2_status`     varchar(20) DEFAULT '' COMMENT '通道2状态',
    `out3_electric`   varchar(50) DEFAULT '' COMMENT '通道3电流',
    `out3_power`      varchar(50) DEFAULT '' COMMENT '通道3功率',
    `out3_status`     varchar(20) DEFAULT '' COMMENT '通道3状态',
    `out4_electric`   varchar(50) DEFAULT '' COMMENT '通道4电流',
    `out4_power`      varchar(50) DEFAULT '' COMMENT '通道4功率',
    `out4_status`     varchar(20) DEFAULT '' COMMENT '通道4状态',
    `out5_electric`   varchar(50) DEFAULT '' COMMENT '通道5电流',
    `out5_power`      varchar(50) DEFAULT '' COMMENT '通道5功率',
    `out5_status`     varchar(20) DEFAULT '' COMMENT '通道5状态',
    `out6_electric`   varchar(50) DEFAULT '' COMMENT '通道6电流',
    `out6_power`      varchar(50) DEFAULT '' COMMENT '通道6功率',
    `out6_status`     varchar(20) DEFAULT '' COMMENT '通道6状态',
    `out7_electric`   varchar(50) DEFAULT '' COMMENT '通道7电流',
    `out7_power`      varchar(50) DEFAULT '' COMMENT '通道7功率',
    `out7_status`     varchar(20) DEFAULT '' COMMENT '通道7状态',
    `out8_electric`   varchar(50) DEFAULT '' COMMENT '通道8电流',
    `out8_power`      varchar(50) DEFAULT '' COMMENT '通道8功率',
    `out8_status`     varchar(20) DEFAULT '' COMMENT '通道8状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PDU电源分配单元数据表';

-- 6. 功放数据表
CREATE TABLE `bu_data_amplifier`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sn`             varchar(50)  DEFAULT '' COMMENT 'SN码',
    `device_id`      bigint       DEFAULT NULL COMMENT '设备ID',
    `device_code`    varchar(50)  DEFAULT '' COMMENT '设备编码',
    `initial_time`    bigint      DEFAULT NULL COMMENT '录入时间戳',
    `initial_bj_time` datetime    DEFAULT NULL COMMENT '录入时间(北京时间)',
    `decay`           varchar(50) DEFAULT '' COMMENT '衰减值',
    `temp`            varchar(50) DEFAULT '' COMMENT '温度',
    `out_power`       varchar(50) DEFAULT '' COMMENT '输出功率',
    `status`          varchar(20) DEFAULT '' COMMENT '设备状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功放数据表';